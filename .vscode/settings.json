{
    "editor.cursorWidth": 180,
    "java.compile.nullAnalysis.mode": "automatic",
    "java.configuration.updateBuildConfiguration": "automatic",
    "java.format.enabled": true,
    "editor.formatOnSave": true,
    // "java.format.settings.url": "https://gh-proxy.com/raw.githubusercontent.com/google/styleguide/gh-pages/eclipse-java-google-style.xml",
    "java.format.settings.profile": "GoogleStyle",
    "[java]": {
        "editor.defaultFormatter": "redhat.java"
    },
    "java.debug.settings.onBuildFailureProceed": true
}
