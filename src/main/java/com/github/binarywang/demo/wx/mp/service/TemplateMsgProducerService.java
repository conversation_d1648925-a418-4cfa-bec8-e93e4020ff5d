package com.github.binarywang.demo.wx.mp.service;

import com.github.binarywang.demo.wx.mp.dto.BatchSendResult;
import com.github.binarywang.demo.wx.mp.dto.BatchTemplateMsgRequest;
import com.github.binarywang.demo.wx.mp.dto.TemplateMsgTask;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 模板消息生产者服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TemplateMsgProducerService {

    private final RedisQueueService queueService;
    private final RedisTemplate<String, Object> redisTemplate;

    private static final String BATCH_RESULT_KEY_PREFIX = "batch_result:";
    private static final int BATCH_RESULT_EXPIRE_HOURS = 24;

    /**
     * 发送单个模板消息到队列
     *
     * @param task 消息任务
     */
    public void sendSingleMessage(TemplateMsgTask task) {
        try {
            // 设置任务ID和创建时间
            if (task.getTaskId() == null) {
                task.setTaskId(UUID.randomUUID().toString());
            }
            if (task.getCreateTime() == null) {
                task.setCreateTime(LocalDateTime.now());
            }

            // 发送到 Redis 队列
            queueService.pushTask(task);

            log.info("Single template message sent to queue, taskId: {}, toUser: {}",
                    task.getTaskId(), task.getToUser());

        } catch (Exception e) {
            log.error("Failed to send single template message to queue", e);
            throw new RuntimeException("发送消息到队列失败", e);
        }
    }

    /**
     * 批量发送模板消息到队列
     *
     * @param request 批量发送请求
     * @return 批次ID
     */
    public String sendBatchMessages(BatchTemplateMsgRequest request) {
        String batchId = request.getBatchId();
        if (batchId == null) {
            batchId = UUID.randomUUID().toString();
            request.setBatchId(batchId);
        }

        try {
            // 初始化批次统计信息
            BatchSendResult batchResult = new BatchSendResult();
            batchResult.setBatchId(batchId);
            batchResult.setTotalCount(request.getToUsers().size());
            batchResult.setProcessingCount(request.getToUsers().size());
            batchResult.setStartTime(LocalDateTime.now());
            batchResult.setStatus(BatchSendResult.SendStatus.PROCESSING);

            // 估算完成时间（基于限流速度）
            int totalMessages = request.getToUsers().size();
            int messagesPerMinute = 100; // 微信API限制
            long estimatedMinutes = (totalMessages + messagesPerMinute - 1) / messagesPerMinute;
            batchResult.setEstimatedFinishTime(LocalDateTime.now().plusMinutes(estimatedMinutes));

            // 保存批次统计信息到Redis
            saveBatchResult(batchResult);

            // 将批量请求拆分为单个任务并发送到队列
            for (String toUser : request.getToUsers()) {
                TemplateMsgTask task = new TemplateMsgTask();
                task.setTaskId(UUID.randomUUID().toString());
                task.setBatchId(batchId);
                task.setToUser(toUser);
                task.setTemplateId(request.getTemplateId());
                task.setUrl(request.getUrl());
                task.setMiniProgram(request.getMiniProgram());
                task.setData(request.getData());
                task.setPriority(request.getPriority());
                task.setCreateTime(LocalDateTime.now());

                // 发送到 Redis 队列
                queueService.pushTask(task);
            }

            log.info("Batch template messages sent to queue, batchId: {}, count: {}",
                    batchId, request.getToUsers().size());

            return batchId;

        } catch (Exception e) {
            log.error("Failed to send batch template messages to queue, batchId: {}", batchId, e);

            // 更新批次状态为失败
            BatchSendResult batchResult = getBatchResult(batchId);
            if (batchResult != null) {
                batchResult.setStatus(BatchSendResult.SendStatus.FAILED);
                batchResult.setFinishTime(LocalDateTime.now());
                saveBatchResult(batchResult);
            }

            throw new RuntimeException("批量发送消息到队列失败", e);
        }
    }

    /**
     * 更新批次发送结果
     *
     * @param batchId 批次ID
     * @param success 是否成功
     */
    public void updateBatchResult(String batchId, boolean success) {
        BatchSendResult batchResult = getBatchResult(batchId);
        if (batchResult == null) {
            log.warn("Batch result not found for batchId: {}", batchId);
            return;
        }

        // 更新统计数据
        if (success) {
            batchResult.setSuccessCount(batchResult.getSuccessCount() + 1);
        } else {
            batchResult.setFailedCount(batchResult.getFailedCount() + 1);
        }
        batchResult.setProcessingCount(batchResult.getProcessingCount() - 1);

        // 检查是否完成
        if (batchResult.getProcessingCount() <= 0) {
            batchResult.setStatus(BatchSendResult.SendStatus.COMPLETED);
            batchResult.setFinishTime(LocalDateTime.now());
        }

        saveBatchResult(batchResult);
    }

    /**
     * 获取批次发送结果
     *
     * @param batchId 批次ID
     * @return 批次发送结果
     */
    public BatchSendResult getBatchResult(String batchId) {
        String key = BATCH_RESULT_KEY_PREFIX + batchId;
        return (BatchSendResult) redisTemplate.opsForValue().get(key);
    }

    /**
     * 保存批次发送结果
     *
     * @param batchResult 批次发送结果
     */
    private void saveBatchResult(BatchSendResult batchResult) {
        String key = BATCH_RESULT_KEY_PREFIX + batchResult.getBatchId();
        redisTemplate.opsForValue().set(key, batchResult, BATCH_RESULT_EXPIRE_HOURS, TimeUnit.HOURS);
    }
}
