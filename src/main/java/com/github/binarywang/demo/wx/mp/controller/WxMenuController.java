package com.github.binarywang.demo.wx.mp.controller;

import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import me.chanjar.weixin.common.api.WxConsts;
import me.chanjar.weixin.common.bean.menu.WxMenu;
import me.chanjar.weixin.common.bean.menu.WxMenuButton;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.menu.WxMpGetSelfMenuInfoResult;
import me.chanjar.weixin.mp.bean.menu.WxMpMenu;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.net.MalformedURLException;
import java.net.URL;

import static me.chanjar.weixin.common.api.WxConsts.MenuButtonType;

@AllArgsConstructor
@Tag(name = "自定义菜单")
@RestController
@RequestMapping("/api/wx/{appid}/menu")
public class WxMenuController {
    private final WxMpService wxService;

    @Operation(summary = "获取自定义菜单配置", externalDocs = @ExternalDocumentation(description = "微信官方文档", url = "https://developers.weixin.qq.com/doc/service/guide/product/menu/intro.html"))
    @GetMapping("")
    public WxMpMenu menuGet(@PathVariable String appid) throws WxErrorException {
        return this.wxService.switchoverTo(appid).getMenuService().menuGet();
    }

    @Operation(summary = "创建自定义菜单", externalDocs = @ExternalDocumentation(description = "微信官方文档", url = "https://developers.weixin.qq.com/doc/service/api/custommenu/api_createcustommenu.html"))
    @PostMapping("")
    public String menuCreate(@PathVariable String appid, @RequestBody WxMenu menu) throws WxErrorException {
        return this.wxService.switchoverTo(appid).getMenuService().menuCreate(menu);
    }

    @Operation(summary = "删除自定义菜单", externalDocs = @ExternalDocumentation(description = "微信官方文档", url = "https://developers.weixin.qq.com/doc/service/api/custommenu/api_deletemenu.html"))
    @DeleteMapping("")
    public void menuDelete(@PathVariable String appid) throws WxErrorException {
        this.wxService.switchoverTo(appid).getMenuService().menuDelete();
    }

    @Operation(summary = "查询自定义菜单信息", description = "提供公众号当前使用的自定义菜单的配置，如果公众号是通过 API 调用设置的菜单，则返回菜单的开发配置，而如果公众号是在公众平台官网通过网站功能发布菜单，则本接口返回运营者设置的菜单配置。", externalDocs = @ExternalDocumentation(description = "微信官方文档", url = "https://developers.weixin.qq.com/doc/service/api/custommenu/api_getcurrentselfmenuinfo.html"))
    @GetMapping("/getSelfMenuInfo")
    public WxMpGetSelfMenuInfoResult getSelfMenuInfo(@PathVariable String appid) throws WxErrorException {
        return this.wxService.switchoverTo(appid).getMenuService().getSelfMenuInfo();
    }

    @Operation(summary = "创建示例菜单 TEST", description = "这是一个用于快速创建示例菜单的接口，不要在正式环境中使用。", externalDocs = @ExternalDocumentation(description = "微信官方文档", url = "https://developers.weixin.qq.com/doc/service/api/custommenu/api_createcustommenu.html"))
    @GetMapping("/create/test")
    public String menuCreateSample(@PathVariable String appid) throws WxErrorException, MalformedURLException {
        WxMenu menu = new WxMenu();
        WxMenuButton button1 = new WxMenuButton();
        button1.setType(MenuButtonType.CLICK);
        button1.setName("今日歌曲");
        button1.setKey("V1001_TODAY_MUSIC");

        // WxMenuButton button2 = new WxMenuButton();
        // button2.setType(WxConsts.BUTTON_MINIPROGRAM);
        // button2.setName("小程序");
        // button2.setAppId("wx286b93c14bbf93aa");
        // button2.setPagePath("pages/lunar/index.html");
        // button2.setUrl("http://mp.weixin.qq.com");

        WxMenuButton button3 = new WxMenuButton();
        button3.setName("菜单");

        menu.getButtons().add(button1);
        // menu.getButtons().add(button2);
        menu.getButtons().add(button3);

        WxMenuButton button31 = new WxMenuButton();
        button31.setType(MenuButtonType.VIEW);
        button31.setName("搜索");
        button31.setUrl("http://www.soso.com/");

        WxMenuButton button32 = new WxMenuButton();
        button32.setType(MenuButtonType.VIEW);
        button32.setName("视频");
        button32.setUrl("http://v.qq.com/");

        WxMenuButton button33 = new WxMenuButton();
        button33.setType(MenuButtonType.CLICK);
        button33.setName("赞一下我们");
        button33.setKey("V1001_GOOD");

        WxMenuButton button34 = new WxMenuButton();
        button34.setType(MenuButtonType.VIEW);
        button34.setName("获取用户信息");

        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder
                .getRequestAttributes();
        if (servletRequestAttributes != null) {
            HttpServletRequest request = servletRequestAttributes.getRequest();
            URL requestURL = new URL(request.getRequestURL().toString());
            String url = this.wxService.switchoverTo(appid).getOAuth2Service().buildAuthorizationUrl(
                    String.format("%s://%s/api/wx/redirect/%s/greet", requestURL.getProtocol(), requestURL.getHost(),
                            appid),
                    WxConsts.OAuth2Scope.SNSAPI_USERINFO, null);
            button34.setUrl(url);
        }

        button3.getSubButtons().add(button31);
        button3.getSubButtons().add(button32);
        button3.getSubButtons().add(button33);
        button3.getSubButtons().add(button34);

        this.wxService.switchover(appid);
        return this.wxService.getMenuService().menuCreate(menu);
    }

}
