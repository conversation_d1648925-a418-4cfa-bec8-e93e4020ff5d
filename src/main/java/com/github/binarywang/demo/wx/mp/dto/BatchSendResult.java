package com.github.binarywang.demo.wx.mp.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 批量发送结果统计
 */
@Data
public class BatchSendResult implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 批次ID
     */
    private String batchId;
    
    /**
     * 总数量
     */
    private Integer totalCount;
    
    /**
     * 成功数量
     */
    private Integer successCount = 0;
    
    /**
     * 失败数量
     */
    private Integer failedCount = 0;
    
    /**
     * 处理中数量
     */
    private Integer processingCount = 0;
    
    /**
     * 发送状态
     */
    private SendStatus status = SendStatus.PENDING;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 完成时间
     */
    private LocalDateTime finishTime;
    
    /**
     * 预计完成时间
     */
    private LocalDateTime estimatedFinishTime;
    
    /**
     * 发送进度百分比
     */
    public Double getProgress() {
        if (totalCount == null || totalCount == 0) {
            return 0.0;
        }
        return (double) (successCount + failedCount) / totalCount * 100;
    }
    
    /**
     * 发送状态枚举
     */
    public enum SendStatus {
        PENDING("待处理"),
        PROCESSING("处理中"),
        COMPLETED("已完成"),
        FAILED("失败");
        
        private final String description;
        
        SendStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
